<label
  class="fin-label fin-flex fin-justify-between fin-items-center"
  [ngClass]="{
    'fin-text-color-text-disabled': control.disabled,
    'fin-text-color-text-primary': readonly,
  }"
  [for]="control"
>
  @if (label) {
    <span class="fin-w-full" finTruncateText> {{ label }} </span>
  } @else {
    <ng-content select="[finFieldLabel]"></ng-content>
  }
</label>
<mat-form-field
  class="fin-field fin-block fin-field-size-{{ size }} "
  [ngClass]="{
    'fin-field-readonly': readonly,
    'fin-field-error':
      (externalFieldMessages?.type || (getMessage$ | async)?.type) === 'error',
    'fin-field-warning':
      (externalFieldMessages?.type || (getMessage$ | async)?.type) ===
      'warning',
    'fin-field-success':
      (externalFieldMessages?.type || (getMessage$ | async)?.type) ===
      'success',
  }"
  [subscriptSizing]="dynamicErrorSpace"
>
  <div
    matPrefix
    class="fin-prefix fin-flex fin-items-center fin-justify-center"
  >
    <ng-content select="[finFieldPrefix]"></ng-content>
  </div>

  @if (autocomplete) {
    @if (aiEnabled) {
      <fin-ai-suggestion
        [aiEnabled]="aiEnabled"
        [formControl]="control"
        (aiSuggestionReady)="onAiSuggestionReady()"
      >
      </fin-ai-suggestion>
    }
    <input
      #finInput
      matInput
      [readonly]="readonly"
      [placeholder]="readonly ? '' : placeholder"
      [formControl]="control"
      [matAutocomplete]="autoComplete"
      finAutocompleteBlockScroll
      (input)="onAutocompleteInputChange()"
      #matAutocompleteTrigger="matAutocompleteTrigger"
      #autocompleteInput
      (blur)="blur($event); onAutocompleteBlur($event)"
      (focus)="onAutocompleteFocus()"
      (keydown.enter)="onAutocompleteEnter($event)"
      finTruncateText
      [disableTooltip]="!readonly"
      [content]="control.value | finExecuteFunc: displayFn"
      [class.fin-pointer-events-auto]="readonly"
      (scroll)="lockReadonlyScroll(finInput)"
    />
    <mat-autocomplete
      #autoComplete="matAutocomplete"
      [autoActiveFirstOption]="
        control.value | finExecuteFunc: controlHasNoValue : options.length
      "
      [hideSingleSelectionIndicator]="true"
      [displayWith]="inputDisplayFn ? inputDisplayFn : displayFn"
      (closed)="onAutocompleteClosed()"
      (opened)="onAutocompleteOpened()"
    >
      @if (options.length) {
        @for (
          option of options;
          let i = $index;
          track option[valuePropertyName]
        ) {
          <mat-option
            #matOption
            [value]="option[valuePropertyName]"
            [class.fin-multiple]="multiple || showChips"
            [class.fin-hidden]="
              showChips &&
              (selectedOptions | finExecuteFunc: checkVisibility : option)
            "
            [class.mdc-list-item--selected]="
              matOption.value
                | finExecuteFunc
                  : isOptionSelected
                  : currentControlValue
                  : multiple
            "
            (mousedown)="$event.preventDefault()"
          >
            <!-- Workaround to enable autocomplete multiselect functionality  -->
            <div
              (click)="onAutocompleteChange($event, option)"
              class="fin-p-[0.8rem] fin-flex fin-items-center"
            >
              <fin-dropdown-option
                class="fin-block"
                [option]="option"
                [isMatOptionsSelected]="showChips ? false : matOption.selected"
                [multiple]="multiple"
                [autocomplete]="autocomplete"
                [showChips]="showChips"
                [optionSuffix]="finOptionSuffixDirective"
                [optionPrefix]="finOptionPrefixDirective"
                [optionLabel]="finOptionLabelDirective"
                [labelPropertyName]="labelPropertyName"
              ></fin-dropdown-option>
              @if (
                ((multiple || showChips) &&
                  control.value.includes(option[valuePropertyName])) ||
                (multiple && matOption.value
                  | finExecuteFunc
                    : isOptionSelected
                    : currentControlValue
                    : multiple)
              ) {
                <mat-pseudo-checkbox
                  class="mat-mdc-option-pseudo-checkbox"
                  state="checked"
                  aria-hidden="true"
                  appearance="minimal"
                ></mat-pseudo-checkbox>
              }
            </div>
          </mat-option>
        }
      }
      @if (
        !options.length ||
        (selectedOptions.length === options.length && showChips)
      ) {
        <mat-option [value]="control.value" class="fin-custom-message">
          @if (control.value && control.value.length >= messageThreshold) {
            @if (finNoResultsMessageDirective) {
              <ng-content select="[finNoResultsMessage]"></ng-content>
            } @else {
              {{ customMessages.noResultsMessage }}
            }
          } @else {
            @if (finInitialMessageDirective) {
              <ng-content select="[finInitialMessage]"></ng-content>
            } @else {
              {{ customMessages.initialMessage }}
            }
          }
        </mat-option>
      }
    </mat-autocomplete>
  } @else {
    <mat-select
      [formControl]="control"
      hideSingleSelectionIndicator
      [placeholder]="readonly ? '' : placeholder"
      [multiple]="multiple || showChips"
      (selectionChange)="onSelectionChange($event)"
      [compareWith]="compareFn"
      (blur)="blur($event)"
    >
      @if (multiple) {
        <mat-select-trigger class="fin-w-full fin-flex justify-between">
          <div
            class="fin-select-trigger-label"
            #triggerLabel
            finTruncateText
            [ngClass]="{
              'fin-pointer-events-auto fin-cursor-not-default fin-select-text':
                readonly,
              'fin-text-color-text-disabled': control.disabled,
              'fin-text-color-text-primary': readonly,
            }"
            [disableTooltip]="!readonly"
          ></div>
          @if (selectedOptionsCounter) {
            <div
              class="fin-select-trigger-label-hidden fin-text-color-text-interactive"
            >
              +{{ selectedOptionsCounter }}
            </div>
          }
        </mat-select-trigger>
      }

      @if (!multiple && readonly) {
        <mat-select-trigger class="fin-w-full fin-flex justify-between">
          <div
            class="fin-select-trigger-label fin-text-color-text-primary fin-pointer-events-auto fin-cursor-not-default fin-select-text"
            finTruncateText
          >
            {{ control.value | finExecuteFunc: displayFn }}
          </div>
        </mat-select-trigger>
      }

      @if (showChips && placeholder) {
        <mat-select-trigger
          class="fin-select-trigger fin-w-full fin-flex justify-between"
        >
          {{ placeholder }}
        </mat-select-trigger>
      }

      @for (option of options; track option[valuePropertyName]) {
        <mat-option
          [value]="option[valuePropertyName]"
          #matOption
          [class.fin-hidden]="selectedOptions.includes(option)"
          (mousedown)="$event.preventDefault()"
        >
          <fin-dropdown-option
            class="fin-block"
            [option]="option"
            [isMatOptionsSelected]="matOption.selected"
            [multiple]="multiple"
            [autocomplete]="autocomplete"
            [showChips]="showChips"
            [optionSuffix]="finOptionSuffixDirective"
            [optionPrefix]="finOptionPrefixDirective"
            [optionLabel]="finOptionLabelDirective"
            [labelPropertyName]="labelPropertyName"
          ></fin-dropdown-option>
        </mat-option>
      }
    </mat-select>
  }

  <div matSuffix class="fin-suffix fin-flex fin-items-center fin-justify-end">
    @if ((control.value | finControlValueLength) && !showChips) {
      <button
        #clearButton
        [disabled]="control.disabled"
        type="button"
        class="btn-clean fin-pr-size-spacing-4"
        (click)="clearField($event)"
        [ngClass]="{
          'fin-hidden': readonly,
        }"
      >
        <fin-icon
          [size]="sizes.M"
          name="close"
          class="fin-dropdown-icons"
        ></fin-icon>
      </button>
    }
    @if (!hideArrow) {
      <button
        #toggleOptionsPanelBtn
        [disabled]="control.disabled"
        type="button"
        class="btn-clean fin-cursor-default"
        (click)="toggleOptionsPanel($event)"
        [class.fin-hidden]="readonly"
      >
        <fin-icon
          [size]="sizes.M"
          name="keyboard_arrow_down"
          class="fin-dropdown-arrow fin-dropdown-icons"
          [ngClass]="{
            'fin-rotate-180': matSelect?.panelOpen || autocompleteField?.isOpen,
            'fin-hidden': readonly,
          }"
        ></fin-icon>
      </button>
    }

    <div
      #suffixWrapper
      class="fin-flex fin-items-center"
      (click)="$event.stopPropagation()"
    >
      <ng-content select="[finFieldSuffix]"></ng-content>
    </div>
  </div>
  <mat-hint class="fin-w-full">
    @if (
      control.valid && control.touched && (getMessage$ | async);
      as message
    ) {
      @if (message.type === 'success') {
        <ng-container *ngTemplateOutlet="message.template"></ng-container>
      }
    } @else {
      <div class="fin-hint">
        <ng-content select="[finFieldHint]"></ng-content>
      </div>
    }
  </mat-hint>

  @if ((getMessage$ | async)?.template; as messageTemplate) {
    <mat-error [@.disabled]="true">
      <div class="fin-inline-block fin-w-full">
        <ng-container *ngTemplateOutlet="messageTemplate"></ng-container>
      </div>
    </mat-error>
  }
</mat-form-field>

@if (showChips) {
  <fin-dropdown-chips
    (removeOption)="onRemovedChip($event)"
    [options]="selectedOptions"
    [chipPrefix]="finChipPrefixDirective"
    [chipSuffix]="finChipSuffixDirective"
    [showClose]="!readonly"
  >
  </fin-dropdown-chips>
}
