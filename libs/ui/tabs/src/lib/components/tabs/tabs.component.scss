$color-text-interactive: theme('colors.color-text-interactive');
$color-text-tertiary: theme('colors.color-text-tertiary');
$color-background-tertiary-minimal: theme(
  'colors.color-background-tertiary-minimal'
);
$color-border-default-hover: theme('colors.color-border-default-hover');

::ng-deep {
  // default
  .fin-tabs.mat-mdc-tab-group {
    .mat-mdc-tab-body-wrapper {
      transition: unset !important;
      .mat-mdc-tab-body-content {
        transform: none !important;
        min-height: 0 !important;
        background-color: #fff;
      }
    }
    .mat-mdc-tab {
      -webkit-tap-highlight-color: #{$color-border-default-hover};
      --mat-tab-header-label-text-font: theme(
          'fontFamily.text-display-1-family'
        ),
        sans-serif;
      --mat-tab-header-label-text-tracking: normal;

      @apply fin-text-body-2-moderate;

      &.mdc-tab--active {
        @apply fin-text-body-2-strong;
      }

      &.fin-tab-hidden {
        display: none;
      }

      // Hidden bold text clone
      .label-wrapper {
        display: grid !important;
        grid-template-areas: 'stack';
        place-content: stretch;
        &::after {
          content: attr(data-text-label);
          @apply fin-text-body-2-strong;
          @apply fin-opacity-0;
          grid-area: stack;
          pointer-events: none;
          // @apply fin-h-0;
        }
        > .fin-label-template {
          grid-area: stack;
          height: 100%;
        }
      }
    }

    &:not(.fin-tabs-secondary):not(.fin-tabs-secondary-compact) {
      .mat-ripple {
        @apply fin-transition;
        @apply fin-duration-[0.4s];
        @apply fin-ease-in-out;
      }
    }

    &:not(.fin-tabs-tertiary) {
      --mdc-secondary-navigation-tab-container-height: 4rem;

      --mdc-tab-indicator-active-indicator-height: 0.2rem;
      --mdc-tab-indicator-active-indicator-shape: 2.5rem;
      --mdc-tab-indicator-active-indicator-color: #{$color-border-default-hover};

      --mat-tab-header-divider-color: theme(
        'colors.color-border-default-primary'
      );
      --mat-tab-header-divider-height: 0.1rem;
      --mat-tab-header-disabled-ripple-color: transparent;
      --mat-tab-header-pagination-icon-color: #{$color-text-interactive};

      --mat-tab-header-active-label-text-color: #{$color-text-interactive};
      --mat-tab-header-active-ripple-color: transparent;
      --mat-tab-header-active-focus-label-text-color: theme(
        'colors.color-text-primary'
      );
      --mat-tab-header-active-hover-label-text-color: #{$color-text-interactive};
      --mat-tab-header-active-focus-indicator-color: #{$color-border-default-hover};
      --mat-tab-header-active-hover-indicator-color: #{$color-border-default-hover};

      --mat-tab-header-inactive-label-text-color: #{$color-text-tertiary};
      --mat-tab-header-inactive-ripple-color: transparent;
      --mat-tab-header-inactive-focus-label-text-color: #{$color-text-tertiary};
      --mat-tab-header-inactive-hover-label-text-color: #{$color-text-tertiary};

      .mat-mdc-tab {
        &-disabled {
          --mdc-tab-indicator-active-indicator-color: transparent;
          opacity: 1;
          background-color: #{$color-background-tertiary-minimal};
        }

        &-label-container {
          overflow: visible;
        }

        .mdc-tab__text-label {
          @apply fin-text-color-text-primary;
        }
      }
      .mat-mdc-tab:has(a) {
        @apply fin-px-0;
        .mdc-tab__text-label {
          @apply fin-w-full;
          @apply fin-h-full;
          .label-wrapper:has(a) {
            @apply fin-w-full;
            @apply fin-h-full;
            a {
              @apply fin-w-full;
              @apply fin-h-full;
              @apply fin-flex;
              @apply fin-justify-center;
              @apply fin-items-center;
              @apply fin-px-[2.4rem];
            }
            &::after {
              @apply fin-px-[2.4rem];
            }
          }
        }
      }

      .mdc-tab-indicator--active:not(.mat-mdc-tab-disabled) {
        .mat-ripple.mat-mdc-tab-ripple {
          background-color: theme('colors.color-background-light');
        }

        .mdc-tab-indicator {
          &__content {
            border-radius: 0;
            position: absolute;
            bottom: -0.1rem;
          }
        }
      }
    }
  }

  // primary
  .fin-tabs-primary.mat-mdc-tab-group {
    .mat-mdc-tab-header {
      @apply fin-px-[2.4rem];
      background-color: #{$color-background-tertiary-minimal};
      .mat-mdc-tab {
        @apply fin-px-[2.4rem];
        &-ripple {
          border-radius: 0.8rem 0.8rem 0 0;
          margin-top: 0.6rem;
        }
        &-label-container {
          border-bottom: 0;
        }
        .mdc-tab-indicator {
          display: none;
        }
        .mdc-tab-indicator--active:not(.mat-mdc-tab-disabled) {
          .mat-ripple.mat-mdc-tab-ripple {
            border-radius: 0.8rem 0.8rem 0 0;
          }
        }
        &:hover:not(.mdc-tab--active) {
          .mat-ripple {
            @apply fin-bg-color-hover-tertiary;
          }
        }
      }
    }
  }

  // secondary, secondary compact
  .fin-tabs-secondary.mat-mdc-tab-group,
  .fin-tabs-secondary-compact.mat-mdc-tab-group {
    .mat-mdc-tab {
      @apply fin-px-[1.6rem];
      @apply fin-transition;
      @apply fin-duration-[0.4s];
      @apply fin-ease-in-out;
      &-label-container {
        background-color: theme('colors.color-background-light');
      }
      &:hover:not(.mdc-tab--active) {
        @apply fin-bg-color-hover-tertiary;
      }
    }
  }

  // secondary
  .fin-tabs-secondary.mat-mdc-tab-group {
    .mat-mdc-tab {
      &-list {
        padding-left: 2.4rem;
        padding-right: 2.4rem;
      }
    }
  }

  // tertiary
  .fin-tabs-tertiary.mat-mdc-tab-group {
    $border-radius: 2rem;
    $active-tab-background: theme('colors.color-background-tertiary-minimal');
    $inactive-tab-background: theme('colors.color-surface-secondary');
    $text-color: theme('colors.color-text-primary');

    .mat-ripple {
      &.mat-mdc-tab-header-pagination {
        @apply fin-mx-[0.6rem];
        @apply fin-my-[0.8rem];
        @apply fin-h-[2.4rem];
        @apply fin-min-w-8;
        @apply fin-rounded-size-corner-radius-s;

        &:not(.mat-mdc-tab-header-pagination-disabled) {
          @apply fin-text-color-icons-interactive;
        }

        &:hover {
          @apply fin-bg-color-hover-informative;
        }
      }
    }

    .mat-mdc-tab {
      @apply fin-px-[1.6rem];

      .label-wrapper::after {
        @apply fin-max-w-[22rem];
        @apply fin-truncate;
      }

      &:not(:last-child) {
        margin-right: 1.2rem;
      }
      &-ripple {
        border-radius: $border-radius;
        color: $inactive-tab-background;
      }
      &:not(.mdc-tab--stacked) {
        --mdc-secondary-navigation-tab-container-height: 4.2rem;
      }
      .mdc-tab__ripple::before {
        border-radius: $border-radius;
      }
    }

    &.mat-mdc-tab-group,
    &.mat-mdc-tab-nav-bar {
      --mdc-tab-indicator-active-indicator-color: transparent;
      --mat-tab-header-disabled-ripple-color: transparent;
      --mat-tab-header-pagination-icon-color: $text-color;
      --mat-tab-header-inactive-label-text-color: $text-color;
      --mat-tab-header-active-label-text-color: $text-color;
      --mat-tab-header-active-ripple-color: transparent;
      --mat-tab-header-inactive-ripple-color: theme(
        'colors.color-background-dark-moderate'
      );
      --mat-tab-header-inactive-focus-label-text-color: $text-color;
      --mat-tab-header-inactive-hover-label-text-color: $text-color;
      --mat-tab-header-active-focus-label-text-color: $text-color;
      --mat-tab-header-active-hover-label-text-color: $text-color;
      --mat-tab-header-active-focus-indicator-color: transparent;
      --mat-tab-header-active-hover-indicator-color: transparent;
    }

    .mdc-tab:hover:not(.mdc-tab--active) {
      .mat-mdc-tab-ripple {
        background-color: theme('colors.color-hover-tertiary');
      }
    }

    .mdc-tab-indicator--active {
      .mat-ripple.mat-mdc-tab-ripple {
        background-color: $active-tab-background;
      }
    }
  }

  // size L
  .fin-tabs-l.mat-mdc-tab-group {
    .mat-mdc-tab {
      &:not(.mdc-tab--stacked) {
        --mdc-secondary-navigation-tab-container-height: 40px;
      }
    }
  }

  // size XL
  .fin-tabs-xl.mat-mdc-tab-group {
    .mat-mdc-tab {
      &:not(.mdc-tab--stacked) {
        --mdc-secondary-navigation-tab-container-height: 48px;
      }
    }
  }
}
