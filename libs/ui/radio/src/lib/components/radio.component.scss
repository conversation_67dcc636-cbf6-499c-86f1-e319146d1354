$color-icons-interactive: theme('colors.color-icons-interactive');
$color-icons-primary: theme('colors.color-icons-primary');
$color-border-default-primary: theme('colors.color-border-default-primary');
$color-border-default-hover: theme('colors.color-border-default-hover');
$color-background-neutral-minimal: theme(
  'colors.color-background-neutral-minimal'
);
$color-background-secondary-subtle: theme(
  'colors.color-background-secondary-subtle'
);
$color-background-tertiary-subtle: theme(
  'colors.color-background-tertiary-subtle'
);
$color-background-secondary-minimal: theme(
  'colors.color-background-secondary-minimal'
);
$color-background-disabled: theme('colors.color-background-disabled');
$color-border-default-inactive: theme('colors.color-border-default-inactive');
$color-icons-brand-ai: theme('colors.color-icons-brand-ai');
$color-text-primary: theme('colors.color-text-primary');
$color-background-secondary-moderate: theme(
  'colors.color-background-secondary-moderate'
);
$color-color-hover-neutral: theme('colors.color-hover-neutral');

.mat-mdc-radio-button {
  --mdc-radio-unselected-hover-icon-color: #{$color-border-default-hover};
  --mdc-radio-selected-focus-icon-color: #{$color-icons-interactive};
  --mdc-radio-selected-hover-icon-color: #{$color-icons-primary};
  --mdc-radio-selected-icon-color: #{$color-icons-interactive};
  --mdc-radio-selected-pressed-icon-color: #{$color-icons-interactive};
  --mat-radio-checked-ripple-color: #{$color-icons-interactive};
  --mdc-radio-disabled-selected-icon-opacity: 1;
  --mdc-radio-disabled-unselected-icon-opacity: 1;
  --mat-radio-checked-ripple-color: transparent;
  --mat-radio-ripple-color: transparent;
  --mdc-radio-unselected-icon-color: #{$color-border-default-primary};
  --mdc-radio-disabled-unselected-icon-color: #{$color-border-default-inactive};
  --mdc-radio-disabled-selected-icon-color: #{$color-background-secondary-moderate};
  --mdc-radio-state-layer-size: 2.4rem;
  --mat-radio-touch-target-display: none;
  --mdc-form-field-label-text-color: #{$color-text-primary};
}

:host ::ng-deep {
  .mat-mdc-radio-button {
    .mdc-radio__outer-circle {
      border-width: 0.1rem;
      background-color: $color-background-neutral-minimal;
    }
    .mdc-form-field--align-end .mdc-label {
      padding-inline-end: 1.2rem;
    }
    :not(.mdc-form-field--align-end) .mdc-label {
      padding-inline-start: 1.2rem;
    }
    &:hover {
      .mdc-radio__outer-circle {
        background-color: $color-color-hover-neutral;
      }
    }
  }

  .mat-mdc-radio-checked {
    .mdc-radio__outer-circle {
      background-color: $color-background-secondary-subtle;
      border-width: 0;
    }
    .mdc-radio {
      &:hover {
        .mdc-radio__outer-circle {
          background-color: $color-background-tertiary-subtle;
        }
      }
      .mdc-radio__native-control {
        &:disabled {
          &:checked {
            + .mdc-radio__background {
              .mdc-radio__outer-circle {
                background-color: $color-background-secondary-minimal;
              }
            }
          }
          .mdc-radio__outer-circle {
            background-color: $color-background-disabled;
          }
        }
      }
    }
  }

  // FinSize M
  .fin-radio-m {
    .mat-mdc-radio-button {
      .mdc-radio__background,
      .mdc-radio {
        width: 2.4rem;
        height: 2.4rem;
        padding: 0;
      }

      .mdc-radio {
        &__inner-circle {
          border-width: 1.2rem;
        }
      }

      .mdc-label {
        @apply fin-text-body-2-moderate;
      }
    }
  }

  // FinSize S
  .fin-radio-s {
    .mat-mdc-radio-button {
      .mdc-radio__background,
      .mdc-radio {
        width: 1.6rem;
        height: 1.6rem;
        padding: 0;
      }

      .mdc-radio {
        &__inner-circle {
          border-width: 0.8rem;
        }
      }

      .mdc-label {
        @apply fin-text-body-3-moderate;
      }
    }
  }
}

::ng-deep fin-radio-button {
  .ng-touched.ng-invalid,
  .ng-dirty.ng-invalid {
    .mdc-radio__native-control:enabled:not(.mat-mdc-radio-checked) {
      ~ .mdc-radio__background {
        .mdc-radio__outer-circle {
          @apply fin-border-color-border-default-error #{!important};
        }
      }
    }
    .mat-mdc-radio-button:hover {
      .mdc-radio__native-control:enabled:not(.mat-mdc-radio-checked) {
        ~ .mdc-radio__background {
          .mdc-radio__outer-circle {
            @apply fin-bg-color-hover-neutral #{!important};
          }
        }
      }
    }
  }
}
