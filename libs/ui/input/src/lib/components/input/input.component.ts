import { CommonModule } from '@angular/common';
import {
  AfterViewInit,
  ChangeDetectionStrategy,
  Component,
  DestroyRef,
  EventEmitter,
  Inject,
  Injector,
  Input,
  LOCALE_ID,
  OnChanges,
  OnInit,
  Optional,
  Output,
  SimpleChanges,
  booleanAttribute,
  forwardRef,
  numberAttribute,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { NG_VALUE_ACCESSOR, ReactiveFormsModule } from '@angular/forms';
import { FinAiSuggestionComponent } from '@fincloud/ui/ai-suggestion';
import { FinLoaderModule } from '@fincloud/ui/loader';
import {
  FIN_MAT_FORM_FIELD_DEFAULT_OPTIONS,
  FinAngularMaterialModule,
} from '@fincloud/utils/angular-material';
import { FinControlValueAccessor } from '@fincloud/utils/control-value-accessor';
import { FinFieldService } from '@fincloud/utils/services';
import { shareReplay, startWith } from 'rxjs';

import {
  FinFieldMessageBodyDirective,
  FinFieldMessageService,
} from '@fincloud/ui/field-message';
import { FinIconModule } from '@fincloud/ui/icon';
import { FinTruncateTextModule } from '@fincloud/ui/truncate-text';
import { FinErrorSpace, FinSize } from '@fincloud/ui/types';
import { NgxCurrencyConfig, NgxCurrencyDirective } from 'ngx-currency';
import { FinInputType } from '../../enums/fin-input-type';
import { LocaleId } from '../../enums/fin-locale-id';
import { FIN_CURRENCY_MASK } from '../../utils/fin-currency-mask-token';
import { FIN_DECIMAL_MASK } from '../../utils/fin-decimal-mask-token';
import { FIN_INTEGER_MASK } from '../../utils/fin-integer-mask-token';
import { FIN_MONTHS_MASK } from '../../utils/fin-months-mask-token';
import { FIN_PERCENTAGE_MASK } from '../../utils/fin-percentage-mask-token';
import { FIN_REGION_LOCALE_ID } from '../../utils/fin-region-locale-id';

/**
 * A field where users can enter data.
 *
 * {@link https://lib-ui.neoshare.dev/?path=/docs/components-input--docs Storybook Reference}
 */
@Component({
  selector: 'fin-input',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FinAngularMaterialModule,
    FinLoaderModule,
    NgxCurrencyDirective,
    FinTruncateTextModule,
    FinIconModule,
    FinAiSuggestionComponent,
    FinTruncateTextModule,
  ],
  templateUrl: './input.component.html',
  styleUrl: './input.component.scss',
  host: {
    class: 'fin-input',
  },
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => FinInputComponent),
      multi: true,
    },
    FIN_MAT_FORM_FIELD_DEFAULT_OPTIONS,
    FinFieldService,
    FinFieldMessageService,
  ],
})
export class FinInputComponent
  extends FinControlValueAccessor
  implements OnInit, AfterViewInit, OnChanges
{
  /** Label for the input field */
  @Input() label = '';

  /** The placeholder for this control. */
  @Input() placeholder = '';

  /** FinSize of the input. It should be from type FinSize. Default is Medium (FinSize.Medium). */
  @Input() size: FinSize.M | FinSize.L = FinSize.M;

  /** The type of the input field. Support the options from FinInputType enum. Default is FinInputType.Text. */
  @Input() type: FinInputType = FinInputType.TEXT;

  /** Show/hide the eye icon for switching the password filed from hidden to visible. It works only for password type input fields. Default is true. */
  @Input() showPasswordButton = true;

  /** Show/hide progress bar under the input field. Default is false. */
  @Input({ transform: booleanAttribute }) showProgress = false;

  /** Show/hide loader in the suffix of the input component. Default is false. */
  @Input({ transform: booleanAttribute }) showLoader = false;

  /** Switch readonly mode. */
  @Input({ transform: booleanAttribute }) readonly = false;

  /**
   * Enables AI suggestion animations. When `true`, suggestions appear on input changes and auto-hide after certain time.
   * Animation only triggers when BOTH `aiEnabled` is `true` AND input value changes.
   */
  @Input({ transform: booleanAttribute }) aiEnabled = false;

  /** Specifies the number of maximum characters in the input. */
  @Input({ transform: numberAttribute }) maxLength = 0;

  /** Specifies the minimum allowed value for the input. */
  @Input({ transform: numberAttribute }) min: number | null = null;

  /** Specifies the maximum allowed value for the input. */
  @Input({ transform: numberAttribute }) max: number | null = null;

  /** The input mask of the field. */
  @Input()
  get mask(): NgxCurrencyConfig {
    return this._mask;
  }
  set mask(config: Record<LocaleId, NgxCurrencyConfig> | NgxCurrencyConfig) {
    if (config && this.isFinMaskConfig(config)) {
      this._mask = config;
    } else if (config) {
      this._mask = config[this.localeId];
    }
  }
  private _mask = {} as NgxCurrencyConfig;

  /** Whether the form field should reserve space for error. */
  @Input() dynamicErrorSpace!: FinErrorSpace;

  /** To be used only with external messages */
  @Input() externalFieldMessages: FinFieldMessageBodyDirective | null = null;

  /**
   * Emitted when the AI suggestion animation completes.
   * Only emits when the animation completes naturally, not when `aiEnabled` is disabled.
   */
  @Output() aiSuggestionReady = new EventEmitter<void>();

  passwordTypeToggle = false;
  finInputType = FinInputType;

  /** Returns the active validation message. */
  protected getMessage$ = this.finFieldMessageService?.getMessage$.pipe(
    shareReplay({ refCount: true, bufferSize: 1 }),
  );

  constructor(
    injector: Injector,

    private finFieldService: FinFieldService,

    @Optional() private finFieldMessageService: FinFieldMessageService,

    private destroyRef: DestroyRef,

    @Inject(FIN_CURRENCY_MASK)
    private finCurrencyMask: Record<LocaleId, NgxCurrencyConfig>,

    @Inject(FIN_PERCENTAGE_MASK)
    private finPercentageMask: Record<LocaleId, NgxCurrencyConfig>,

    @Inject(FIN_DECIMAL_MASK)
    private finDecimalMask: Record<LocaleId, NgxCurrencyConfig>,

    @Inject(FIN_INTEGER_MASK)
    private finIntegerMask: Record<LocaleId, NgxCurrencyConfig>,

    @Inject(FIN_MONTHS_MASK)
    private finMonthsMask: Record<LocaleId, NgxCurrencyConfig>,

    @Inject(LOCALE_ID)
    private localeId: LocaleId,

    @Inject(FIN_REGION_LOCALE_ID)
    private regionalLocaleId: LocaleId,
  ) {
    super(injector);
  }

  ngOnInit(): void {
    this.trimByMaxLength();
    this.setupInputMask();

    this.control.valueChanges
      .pipe(startWith(this.control.value), takeUntilDestroyed(this.destroyRef))
      .subscribe((value) => {
        this.finFieldService.value$.next(value);
      });
  }

  private trimByMaxLength() {
    this.finFieldService.maxLength = this.maxLength;
    if (
      this.maxLength &&
      !this.finFieldService.isInMaxLength(this.control.value)
    ) {
      this.control.setValue(
        this.finFieldService.trimByMaxLength(this.control.value),
        { emitEvent: false },
      );
    }
  }

  private setupInputMask() {
    const localeId = this.regionalLocaleId;
    const isCustomMaskAvailable = Object.keys(this.mask).length;

    if (!isCustomMaskAvailable && this.type === FinInputType.CURRENCY) {
      this.mask = this.finCurrencyMask[localeId];
    }

    if (!isCustomMaskAvailable && this.type === FinInputType.PERCENTAGE) {
      this.mask = this.finPercentageMask[localeId];
    }

    if (!isCustomMaskAvailable && this.type === FinInputType.DECIMAL) {
      this.mask = this.finDecimalMask[localeId];
    }

    if (!isCustomMaskAvailable && this.type === FinInputType.INTEGER) {
      this.mask = this.finIntegerMask[localeId];
    }

    if (!isCustomMaskAvailable && this.type === FinInputType.MONTHS) {
      this.mask = this.finMonthsMask[localeId];
    }

    if (this.max || this.max === 0) {
      this._mask.max = this.max;
    }

    if (this.min || this.min === 0) {
      this._mask.min = this.min;
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (
      changes['readonly']?.currentValue === false &&
      changes['readonly']?.previousValue === true &&
      !this.control.disabled
    ) {
      this.control.enable({ emitEvent: false });
    }

    // if (changes['readonly']?.previousValue === true && this.control.disabled) {
    //   this.control.disable({ emitEvent: false });
    // }
  }

  ngAfterViewInit(): void {
    this.control.statusChanges
      .pipe(startWith(this.control.errors), takeUntilDestroyed(this.destroyRef))
      .subscribe(() => {
        this.finFieldService.controlErrors$.next(this.control.errors);
      });
  }

  private isFinMaskConfig(value: any): value is NgxCurrencyConfig {
    // Implement a type guard based on the properties of FinMaskConfig
    return (
      value &&
      typeof value.align === 'string' &&
      typeof value.allowNegative === 'boolean'
    );
  }
  protected onAiSuggestionReady() {
    this.aiSuggestionReady.emit();
  }
}
