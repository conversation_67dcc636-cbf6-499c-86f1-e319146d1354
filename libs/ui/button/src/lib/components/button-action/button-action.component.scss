:host {
  @apply fin-flex;
  @apply fin-items-center;
  @apply fin-justify-center;
  @apply fin-p-0 #{!important};
  // @apply fin-p-0;
  // Sizes
  &.fin-button-xxl {
    height: 4.8rem;
    width: 4.8rem;
    ::ng-deep fin-icon {
      height: 4.8rem;
      width: 4.8rem;
      font-size: 4.8rem;
    }
  }
  &.fin-button-xl {
    height: 3.2rem;
    width: 3.2rem;
    ::ng-deep fin-icon {
      height: 3.2rem;
      width: 3.2rem;
      font-size: 3.2rem;
    }
  }
  &.fin-button-l {
    height: 2.4rem;
    width: 2.4rem;
    ::ng-deep fin-icon {
      height: 2.4rem;
      width: 2.4rem;
      font-size: 2.4rem;
    }
  }
  &.fin-button-m {
    height: 2rem;
    width: 2rem;
    ::ng-deep fin-icon {
      height: 2rem;
      width: 2rem;
      font-size: 2rem;
    }
  }
  &.fin-button-s {
    height: 1.6rem;
    width: 1.6rem;
    ::ng-deep fin-icon {
      height: 1.6rem;
      width: 1.6rem;
      font-size: 1.6rem;
    }
  }
  &.fin-button-xs {
    height: 1.2rem;
    width: 1.2rem;
    ::ng-deep fin-icon {
      height: 1.2rem;
      width: 1.2rem;
      font-size: 1.2rem;
    }
  }

  // Types
  &.fin-button-action-primary {
    color: theme('colors.color-icons-primary');
    &:hover {
      background-color: theme('colors.color-hover-neutral');
    }
    &:disabled {
      color: theme('colors.color-icons-disabled');
      background-color: transparent;
      cursor: not-allowed;
    }
  }

  &.fin-button-action-tertiary {
    color: theme('colors.color-icons-tertiary');
    &:hover {
      background-color: theme('colors.color-hover-neutral');
    }
    &:disabled {
      color: theme('colors.color-icons-disabled');
      background-color: transparent;
      cursor: not-allowed;
    }
  }
  &.fin-button-action-informative {
    color: theme('colors.color-icons-interactive');
    &:hover {
      background-color: theme('colors.color-hover-informative');
    }
    &:disabled {
      color: theme('colors.color-icons-disabled');
      background-color: transparent;
      cursor: not-allowed;
    }
  }

  // States
  &:hover {
    &.fin-button-round {
      border-radius: 99999rem;
    }

    &.fin-button-rectangle {
      border-radius: 0.4rem;
    }
  }
}
