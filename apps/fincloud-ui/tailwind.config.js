const { createGlobPatternsForDependencies } = require('@nx/angular/tailwind');
const { join } = require('path');
const tailwindcssThemeProps = require('@fincloud/lib-ui-tokens');
const { fontFamily } = require('tailwindcss/defaultTheme');
const plugin = require('tailwindcss/plugin');

/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    join(__dirname, 'src/**/!(*.stories|*.spec).{ts,html}'),
    ...createGlobPatternsForDependencies(__dirname),
  ],
  prefix: 'fin-',
  important: true,
  theme: {
    extend: {
      boxShadow: {
        small: '0px 1px 8px 0px rgba(38, 40, 62, 0.15)',
        medium: '0px 4px 10px 0px rgba(38, 40, 62, 0.16)',
        large: '0px 15px 30px 0px rgba(38, 40, 62, 0.20)',
      },
    },
  },
  plugins: [
    require('tailwindcss-themer')({
      defaultTheme: {
        extend: {
          ...tailwindcssThemeProps.default.twThemeNeoshareProps,
          fontFamily: {
            ...tailwindcssThemeProps.default.twThemeNeoshareProps.fontFamily,
            sans: ['Chopin', ...fontFamily.sans],
          },
        },
      },
      themes: [
        {
          name: 'theme-volksbank',
          selectors: ['[data-theme="theme-volksbank"]'],
          extend: {
            ...tailwindcssThemeProps.default.twThemeVolksbankProps,
            fontFamily: {
              ...tailwindcssThemeProps.default.twThemeVolksbankProps.fontFamily,
              sans: ['Asterisk Sans Pro', ...fontFamily.sans],
            },
          },
        },
        {
          name: 'theme-sparkasse',
          selectors: ['[data-theme="theme-sparkasse"]'],
          extend: {
            ...tailwindcssThemeProps.default.twThemeSparkasseProps,
            fontFamily: {
              ...tailwindcssThemeProps.default.twThemeSparkasseProps.fontFamily,
              sans: ['Sparkasse Rg', ...fontFamily.sans],
            },
          },
        },
      ],
    }),
    plugin(function (data) {
      data.addComponents(
        tailwindcssThemeProps.default.twComponentsProps.getComponentsProps(
          data.theme,
        ),
      );
    }),
  ],
};
